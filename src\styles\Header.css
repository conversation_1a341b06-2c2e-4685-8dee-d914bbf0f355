/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(10, 10, 10, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-primary);
  transition: var(--transition-normal);
}

.header--scrolled {
  background: rgba(10, 10, 10, 0.95);
  box-shadow: var(--shadow-lg);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) 0;
  min-height: 80px;
}

/* Logo */
.header__logo {
  z-index: var(--z-fixed);
}

.header__logo a {
  display: flex;
  align-items: center;
  font-size: var(--text-2xl);
  font-weight: 700;
  text-decoration: none;
}

.logo__text {
  color: var(--text-primary);
  margin-right: var(--space-1);
}

.logo__accent {
  color: var(--accent-primary);
  position: relative;
}



/* Desktop Navigation */
.header__nav {
  display: none;
}

.nav__list {
  display: flex;
  list-style: none;
  gap: var(--space-8);
}

.nav__item {
  position: relative;
}

.nav__link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: var(--space-2) 0;
  position: relative;
  transition: var(--transition-fast);
}

.nav__link:hover,
.nav__link--active {
  color: var(--text-primary);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--accent-gradient);
  transition: var(--transition-normal);
}

.nav__link:hover::after {
  width: 100%;
}

/* CTA Button */
.header__cta {
  display: none;
}

/* Mobile Menu Button */
.header__menu-btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: var(--z-fixed);
}

.header__menu-btn span {
  display: block;
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-normal);
  transform-origin: center;
}

.menu-btn--open span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.menu-btn--open span:nth-child(2) {
  opacity: 0;
}

.menu-btn--open span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.header__mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-primary);
  overflow: hidden;
}

.mobile-nav {
  padding: var(--space-6) 0;
}

.mobile-nav__list {
  list-style: none;
  margin-bottom: var(--space-6);
}

.mobile-nav__item {
  margin-bottom: var(--space-4);
}

.mobile-nav__link {
  display: block;
  color: var(--text-secondary);
  font-size: var(--text-lg);
  font-weight: 500;
  padding: var(--space-3) var(--space-4);
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.mobile-nav__link:hover,
.mobile-nav__link--active {
  color: var(--text-primary);
  background: var(--glass-bg);
  border-left-color: var(--accent-primary);
}

.mobile-nav__cta {
  padding: 0 var(--space-4);
}

.mobile-nav__cta .btn {
  width: 100%;
  justify-content: center;
}

/* Desktop Styles */
@media (min-width: 768px) {
  .header__nav {
    display: block;
  }
  
  .header__cta {
    display: block;
  }
  
  .header__menu-btn {
    display: none;
  }
  
  .header__mobile-menu {
    display: none;
  }
}

/* Large Desktop */
@media (min-width: 1024px) {
  .header__content {
    padding: var(--space-5) 0;
  }
  
  .nav__list {
    gap: var(--space-10);
  }
}

/* Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Hover Effects */
.header__logo:hover .logo__accent::after {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Authentication Styles */
.header__auth {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-primary);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-outline:hover {
  background: var(--accent-primary);
  color: var(--bg-primary);
  border-color: var(--accent-primary);
}

.btn-admin {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: 2px solid transparent;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-normal);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-admin:hover {
  background: linear-gradient(135deg, #5b21b6, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-menu__trigger {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: transparent;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
}

.user-menu__trigger:hover {
  background: var(--bg-tertiary);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--accent-primary);
  color: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--text-sm);
}

.user-avatar--large {
  width: 40px;
  height: 40px;
  font-size: var(--text-base);
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
}

.user-menu__arrow {
  transition: transform 0.2s ease;
}

.user-menu__arrow--open {
  transform: rotate(180deg);
}

.user-menu__dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  min-width: 280px;
  overflow: hidden;
  z-index: var(--z-dropdown);
}

.user-menu__header {
  padding: var(--space-4);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: var(--bg-tertiary);
}

.user-info__name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.user-info__email {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.user-info__role {
  font-size: var(--text-xs);
  color: var(--accent-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.user-menu__divider {
  height: 1px;
  background: var(--border-primary);
}

.user-menu__items {
  padding: var(--space-2);
}

.user-menu__item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  background: transparent;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: var(--text-sm);
}

.user-menu__item:hover {
  background: var(--bg-tertiary);
}

.user-menu__item--logout {
  color: #ef4444;
}

.user-menu__item--logout:hover {
  background: rgba(239, 68, 68, 0.1);
}

.user-menu__icon {
  font-size: var(--text-base);
}

/* Mobile Authentication */
.mobile-nav__auth {
  margin-top: var(--space-6);
  padding: 0 var(--space-6);
}

.mobile-auth-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mobile-auth-buttons .btn {
  width: 100%;
  justify-content: center;
}

.mobile-user-info {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
}

.mobile-user-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.mobile-user-details {
  flex: 1;
}

.mobile-user-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.mobile-user-email {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.mobile-user-role {
  font-size: var(--text-xs);
  color: var(--accent-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mobile-user-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.mobile-logout-btn {
  margin-top: var(--space-2);
  color: #ef4444;
  border-color: #ef4444;
}

.mobile-logout-btn:hover {
  background: #ef4444;
  color: white;
}

/* Update responsive styles */
@media (max-width: 768px) {
  .header__nav {
    display: none;
  }

  .header__auth {
    display: none;
  }

  .header__menu-btn {
    display: flex;
  }

  .header__mobile-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    overflow: hidden;
  }

  .mobile-nav {
    padding: var(--space-6) 0;
  }

  .mobile-nav__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .mobile-nav__item {
    margin-bottom: var(--space-4);
  }

  .mobile-nav__link {
    display: block;
    padding: var(--space-3) var(--space-6);
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--text-lg);
    transition: var(--transition-normal);
    border-radius: var(--radius-md);
    margin: 0 var(--space-6);
  }

  .mobile-nav__link:hover,
  .mobile-nav__link--active {
    color: var(--accent-primary);
    background: var(--bg-tertiary);
  }
}
